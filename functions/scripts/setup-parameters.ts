#!/usr/bin/env ts-node

import { SSMClient, PutParameterCommand, ParameterType } from "@aws-sdk/client-ssm";
import { fromIni } from "@aws-sdk/credential-provider-ini";

interface ParameterConfig {
  name: string;
  value: string;
  type: ParameterType;
  description: string;
}

interface StageConfig {
  stage: string;
  parameters: {
    frontendUrl: string;
    payrixApiUrl: string;
    payrixPrivateApiKey: string;
    payrixPublicApiKey: string;
  };
}

const stageConfigs: StageConfig[] = [
  {
    stage: "dev",
    parameters: {
      frontendUrl: "http://localhost:5173",
      payrixApiUrl: "https://test-api.payrix.com",
      payrixPrivateApiKey: "c941c2f0f23137ba6ae868662e8d0bf5",
      payrixPublicApiKey: "4523385f4af70adf260eadf0f6ea4e95",
    },
  },
  {
    stage: "prod",
    parameters: {
      frontendUrl: "https://app.auth-clear.com",
      payrixApiUrl: "https://api.auth-clear.com",
      // Note: Update these with actual production keys
      payrixPrivateApiKey: "cc0ad18f4d05755f60975ef5a1fdc245",
      payrixPublicApiKey: "fc4a36fca727518b9415d4125e56a6c7",
    },
  },
];

class ParameterSetup {
  private client: SSMClient;

  constructor() {
    this.client = new SSMClient({
      region: "us-east-1",
      credentials: fromIni({ profile: "payrix" }),
    });
  }

  private generateParameterConfigs(stageConfig: StageConfig): ParameterConfig[] {
    const { stage, parameters } = stageConfig;
    const prefix = `/auth-clear/${stage}`;

    return [
      {
        name: `${prefix}/frontend-url`,
        value: parameters.frontendUrl,
        type: ParameterType.STRING,
        description: `Frontend URL for ${stage} environment`,
      },
      {
        name: `${prefix}/payrix/api-url`,
        value: parameters.payrixApiUrl,
        type: ParameterType.STRING,
        description: `Payrix API URL for ${stage} environment`,
      },
      {
        name: `${prefix}/payrix/private-api-key`,
        value: parameters.payrixPrivateApiKey,
        type: ParameterType.SECURE_STRING,
        description: `Payrix private API key for ${stage} environment (encrypted)`,
      },
      {
        name: `${prefix}/payrix/public-api-key`,
        value: parameters.payrixPublicApiKey,
        type: ParameterType.SECURE_STRING,
        description: `Payrix public API key for ${stage} environment (encrypted)`,
      },
    ];
  }

  async putParameter(config: ParameterConfig, overwrite: boolean = false): Promise<void> {
    try {
      const command = new PutParameterCommand({
        Name: config.name,
        Value: config.value,
        Type: config.type,
        Description: config.description,
        Overwrite: overwrite,
      });

      await this.client.send(command);
      console.log(`✅ Successfully created/updated parameter: ${config.name}`);
    } catch (error) {
      if (error instanceof Error && error.name === "ParameterAlreadyExists") {
        console.log(`⚠️  Parameter already exists: ${config.name} (use --overwrite to update)`);
      } else {
        console.error(`❌ Failed to create parameter ${config.name}:`, error);
        throw error;
      }
    }
  }

  async setupStage(stage: string, overwrite: boolean = false): Promise<void> {
    const stageConfig = stageConfigs.find((config) => config.stage === stage);
    if (!stageConfig) {
      throw new Error(`Unknown stage: ${stage}. Available stages: ${stageConfigs.map((c) => c.stage).join(", ")}`);
    }

    console.log(`\n🚀 Setting up parameters for stage: ${stage}`);
    console.log(`📍 Using AWS profile: payrix`);
    console.log(`🔄 Overwrite existing: ${overwrite}\n`);

    const parameterConfigs = this.generateParameterConfigs(stageConfig);

    for (const config of parameterConfigs) {
      await this.putParameter(config, overwrite);
    }

    console.log(`\n✅ Successfully set up all parameters for stage: ${stage}`);
  }

  async setupAllStages(overwrite: boolean = false): Promise<void> {
    for (const stageConfig of stageConfigs) {
      await this.setupStage(stageConfig.stage, overwrite);
    }
  }
}

async function main() {
  const args = process.argv.slice(2);
  const stage = args.find((arg) => !arg.startsWith("--"));
  const overwrite = args.includes("--overwrite");
  const help = args.includes("--help") || args.includes("-h");

  if (help) {
    console.log(`
Usage: ts-node setup-parameters.ts [stage] [options]

Arguments:
  stage         Stage to setup (dev, prod, or omit for all stages)

Options:
  --overwrite   Overwrite existing parameters
  --help, -h    Show this help message

Examples:
  ts-node setup-parameters.ts dev              # Setup dev stage only
  ts-node setup-parameters.ts prod --overwrite # Setup prod stage with overwrite
  ts-node setup-parameters.ts --overwrite      # Setup all stages with overwrite
    `);
    return;
  }

  try {
    const setup = new ParameterSetup();

    if (stage) {
      await setup.setupStage(stage, overwrite);
    } else {
      await setup.setupAllStages(overwrite);
    }
  } catch (error) {
    console.error("❌ Setup failed:", error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
