import { AxiosError } from "axios";
import { type OnboardingRequest } from "../../functions/merchants/schemas/onboarding.schema.js";
import { logger } from "../../helpers/logger.js";
import { PayrixMerchantResponse } from "../../types/payrix.types.js";
import { createPayrixApiClient } from "./api-client.js";
import { HTTP_STATUS } from "../../constants/http.constants.js";
import { PAYRIX_ERROR_MESSAGES } from "../../constants/payrix.constants.js";

const DEFAULT_ORG_ID = "t1_org_689e2d622d34b3b3d9a9151";

export async function createMerchant(merchantData: OnboardingRequest): Promise<PayrixMerchantResponse> {
  try {
    const apiClient = await createPayrixApiClient();
    const entityPayload = {
      ...merchantData,
      orgEntities: [
        {
          org: DEFAULT_ORG_ID, //default group id for dev
        },
      ],
    };

    const response = await apiClient.post("/entities", entityPayload);

    const entityData = response.data?.response?.data?.[0];
    if (!entityData) {
      throw new Error(PAYRIX_ERROR_MESSAGES.INVALID_RESPONSE_STRUCTURE);
    }

    return entityData;
  } catch (error) {
    const axiosError = error as AxiosError;
    logger.error("Payrix API Error", {
      status: axiosError.response?.status,
      statusText: axiosError.response?.statusText,
      data: axiosError.response?.data,
      message: axiosError.message,
    });

    throw new Error(`Payrix API Error (${axiosError.response?.status}): ${JSON.stringify(axiosError.response?.data || axiosError.message)}`);
  }
}

export async function createPlaidLinkToken(linkTokenData: {
  userId: string;
  countryCode: string;
  redirectUri: string;
}): Promise<{ linkToken: string; requestId: string }> {
  try {
    logger.info("Creating Plaid link token via Payrix", {
      userId: linkTokenData.userId,
      countryCode: linkTokenData.countryCode,
      redirectUri: linkTokenData.redirectUri,
    });

    const response = await apiClient.post("/plaid/linkToken/create", linkTokenData);

    logger.info("Plaid link token creation response", {
      status: response.status,
      hasLinkToken: !!response.data?.Response?.responses?.[0]?.linkToken,
      requestId: response.data?.Response?.responses?.[0]?.requestId,
    });

    const linkTokenResponse = response.data?.Response?.responses?.[0];
    if (!linkTokenResponse?.linkToken) {
      throw new Error(PAYRIX_ERROR_MESSAGES.NO_LINK_TOKEN);
    }

    return {
      linkToken: linkTokenResponse.linkToken,
      requestId: linkTokenResponse.requestId,
    };
  } catch (error) {
    handlePlaidLinkTokenError(error as AxiosError, linkTokenData);
    throw error;
  }
}

function handlePlaidLinkTokenError(
  error: AxiosError,
  linkTokenData: {
    userId: string;
    countryCode: string;
    redirectUri: string;
  }
): void {
  logger.error("Error creating Plaid link token", {
    error,
    userId: linkTokenData.userId,
    countryCode: linkTokenData.countryCode,
  });

  if (error instanceof AxiosError) {
    logger.error("Payrix API error details", {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
    });

    const status = error.response?.status;
    if (status === HTTP_STATUS.BAD_REQUEST) {
      throw new Error(PAYRIX_ERROR_MESSAGES.INVALID_REQUEST_PARAMS);
    } else if (status === HTTP_STATUS.UNAUTHORIZED) {
      throw new Error(PAYRIX_ERROR_MESSAGES.AUTHENTICATION_FAILED);
    } else if (status === HTTP_STATUS.FORBIDDEN) {
      throw new Error(PAYRIX_ERROR_MESSAGES.PLAID_NOT_ENABLED);
    }
  }
}
