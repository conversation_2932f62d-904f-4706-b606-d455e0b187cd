export const PAYRIX_FIELD_MAPPING = {
  CLIENT_IP: "clientIp",
  DISCLOSURE_IP: "disclosureIP",
  MERCHANT_IP: "merchantIp",
} as const;

export type PayrixFieldMapping = typeof PAYRIX_FIELD_MAPPING;

export const getPayrixFieldName = (field: keyof PayrixFieldMapping): string => {
  return PAYRIX_FIELD_MAPPING[field];
};

export type GooglePayEnvironment = "TEST" | "PRODUCTION";

export const isProduction = import.meta.env.VITE_ENVIRONMENT === "production" || import.meta.env.PROD;

export const DEFAULT_GOOGLE_PAY_CONFIG = {
  enabled: true,
  allowedCardNetworks: ["VISA", "MASTERCARD", "AMEX", "DISCOVER"] as const,
  allowedCardAuthMethods: ["PAN_ONLY", "CRYPTOGRAM_3DS"] as const,
  billingAddressRequired: true,
  shippingAddressRequired: false,
  phoneNumberRequired: false,
} as const;
